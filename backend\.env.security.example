# Security Configuration for SynapseAI
# Copy this file to .env and update the values

# JWT Configuration - CRITICAL: Generate secure secrets for production
# Use: openssl rand -hex 64
JWT_SECRET=your-super-secure-jwt-secret-64-chars-minimum-change-this-in-production
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-64-chars-minimum-change-this-in-production
JWT_ISSUER=synapseai
JWT_AUDIENCE=synapseai-users
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Rate Limiting Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=100
MAX_FAILED_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Security Headers
CSP_ENABLED=true
HSTS_ENABLED=true
CSRF_ENABLED=true

# Session Security
SESSION_SECRET=your-super-secure-session-secret-change-this-in-production
SECURE_COOKIES=false
SAME_SITE_COOKIES=strict

# Database Security
DB_SSL_ENABLED=false
DB_SSL_REJECT_UNAUTHORIZED=true

# Redis Security
REDIS_PASSWORD=redis123
REDIS_TLS_ENABLED=false

# Monitoring and Logging
LOG_LEVEL=info
AUDIT_LOG_ENABLED=true
SECURITY_LOG_ENABLED=true

# DataDog Configuration (Optional)
DATADOG_ENABLED=false
DATADOG_API_KEY=
DATADOG_APP_KEY=
DD_TRACE_ENABLED=false
DD_LOGS_ENABLED=false
DD_APM_ENABLED=false
