{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/gateway/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "tsconfig.build.json"}, "monorepo": true, "root": "apps/gateway", "projects": {"gateway": {"type": "application", "root": "apps/gateway", "entryFile": "main", "sourceRoot": "apps/gateway/src", "compilerOptions": {"tsConfigPath": "apps/gateway/tsconfig.app.json"}}}}