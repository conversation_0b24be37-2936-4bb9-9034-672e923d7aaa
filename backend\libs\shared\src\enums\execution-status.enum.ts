export enum ExecutionStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED',
  TIMEOUT = 'TIMEOUT',
}

export enum SessionEventType {
  SESSION_CREATED = 'SESSION_CREATED',
  SESSION_UPDATED = 'SESSION_UPDATED',
  SESSION_DESTROYED = 'SESSION_DESTROYED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SESSION_MEMORY_WARNING = 'SESSION_MEMORY_WARNING',
  SESSION_MEMORY_LIMIT_EXCEEDED = 'SESSION_MEMORY_LIMIT_EXCEEDED',
  SESSION_CROSS_MODULE_UPDATE = 'SESSION_CROSS_MODULE_UPDATE',
  SESSION_RECOVERY_INITIATED = 'SESSION_RECOVERY_INITIATED',
  SESSION_ANALYTICS_UPDATE = 'SESSION_ANALYTICS_UPDATE',
}

export enum NotificationType {
  IN_APP = 'IN_APP',
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  WEBHOOK = 'WEBHOOK',
  PUSH = 'PUSH',
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum AgentEventType {
  AGENT_CREATED = 'AGENT_CREATED',
  AGENT_UPDATED = 'AGENT_UPDATED',
  AGENT_DELETED = 'AGENT_DELETED',
  AGENT_EXECUTION_STARTED = 'AGENT_EXECUTION_STARTED',
  AGENT_EXECUTION_COMPLETED = 'AGENT_EXECUTION_COMPLETED',
  AGENT_EXECUTION_FAILED = 'AGENT_EXECUTION_FAILED',
  AGENT_TOOL_CALL_STARTED = 'AGENT_TOOL_CALL_STARTED',
  AGENT_TOOL_CALL_COMPLETED = 'AGENT_TOOL_CALL_COMPLETED',
  AGENT_KNOWLEDGE_SEARCH = 'AGENT_KNOWLEDGE_SEARCH',
  AGENT_PERFORMANCE_UPDATE = 'AGENT_PERFORMANCE_UPDATE',
}

export enum ToolEventType {
  TOOL_CREATED = 'TOOL_CREATED',
  TOOL_UPDATED = 'TOOL_UPDATED',
  TOOL_DELETED = 'TOOL_DELETED',
  TOOL_EXECUTION_STARTED = 'TOOL_EXECUTION_STARTED',
  TOOL_EXECUTION_COMPLETED = 'TOOL_EXECUTION_COMPLETED',
  TOOL_EXECUTION_FAILED = 'TOOL_EXECUTION_FAILED',
  TOOL_VALIDATION_STARTED = 'TOOL_VALIDATION_STARTED',
  TOOL_VALIDATION_COMPLETED = 'TOOL_VALIDATION_COMPLETED',
}

export enum PromptTemplateEventType {
  TEMPLATE_CREATED = 'TEMPLATE_CREATED',
  TEMPLATE_UPDATED = 'TEMPLATE_UPDATED',
  TEMPLATE_DELETED = 'TEMPLATE_DELETED',
  TEMPLATE_PUBLISHED = 'TEMPLATE_PUBLISHED',
  TEMPLATE_SHARED = 'TEMPLATE_SHARED',
  TEMPLATE_FORKED = 'TEMPLATE_FORKED',
  TEMPLATE_VERSION_CREATED = 'TEMPLATE_VERSION_CREATED',
  TEMPLATE_RENDERED = 'TEMPLATE_RENDERED',
  TEMPLATE_RATED = 'TEMPLATE_RATED',
  TEMPLATE_IMPORTED = 'TEMPLATE_IMPORTED',
  TEMPLATE_EXPORTED = 'TEMPLATE_EXPORTED',
}
