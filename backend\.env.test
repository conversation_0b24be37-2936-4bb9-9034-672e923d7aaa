# Test environment configuration
NODE_ENV=test
PORT=3001

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=synapseai_test

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=15
REDIS_QUEUE_DB=14

# JWT
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_REFRESH_SECRET=test-jwt-refresh-secret-key-for-testing-only
JWT_ISSUER=synapseai-test
JWT_AUDIENCE=synapseai-test-users
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Frontend
FRONTEND_URL=http://localhost:3000

# Monitoring (disabled for tests)
DATADOG_ENABLED=false
LOG_LEVEL=error

# Rate limiting (relaxed for tests)
THROTTLE_TTL=60
THROTTLE_LIMIT=1000

# Service identification
SERVICE_NAME=synapseai-test
SERVICE_VERSION=1.0.0-test
