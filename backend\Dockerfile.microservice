FROM node:18-alpine

# Build argument for service name
ARG SERVICE_NAME

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies including monitoring packages
RUN npm ci --only=production && npm cache clean --force

# Create logs directory
RUN mkdir -p logs && chown -R 1001:1001 logs

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Change ownership of the app directory
RUN chown -R nestjs:nodejs /app
USER nestjs

# Expose port (will be overridden by docker-compose)
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:${PORT:-3000}/health || exit 1

# Start the application
CMD node dist/apps/${SERVICE_NAME}/main
