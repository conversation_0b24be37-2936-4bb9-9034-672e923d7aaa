input {
  beats {
    port => 5044
  }
  
  tcp {
    port => 5000
    codec => json_lines
  }
  
  http {
    port => 8080
    codec => json
  }
}

filter {
  if [fields][service] {
    mutate {
      add_field => { "service_name" => "%{[fields][service]}" }
    }
  }
  
  if [level] {
    mutate {
      add_field => { "log_level" => "%{level}" }
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
  
  if [message] =~ /ERROR/ {
    mutate {
      add_tag => [ "error" ]
    }
  }
  
  if [message] =~ /WARN/ {
    mutate {
      add_tag => [ "warning" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "synapseai-logs-%{+YYYY.MM.dd}"
  }
  
  stdout {
    codec => rubydebug
  }
}
