# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/tempo_ai_platform
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=tempo_ai_platform
DATABASE_SSL=false
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=true
DATABASE_MIGRATIONS_RUN=true

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# AI Provider API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
GOOGLE_AI_API_KEY=your-google-ai-api-key-here
AZURE_OPENAI_API_KEY=your-azure-openai-api-key-here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# External Service APIs
SERP_API_KEY=your-serp-api-key-for-web-search
SENDGRID_API_KEY=your-sendgrid-api-key-for-emails
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token

# Application Configuration
NODE_ENV=development
PORT=3001
API_VERSION=v1
APP_NAME=Tempo AI Platform
APP_URL=http://localhost:3000
API_URL=http://localhost:3001

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md,json,csv

# Monitoring and Logging
LOG_LEVEL=info
DATADOG_API_KEY=your-datadog-api-key
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-for-security
WEBHOOK_TIMEOUT=30000

# Billing and Subscription
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production
CSRF_SECRET=your-csrf-secret-change-this-in-production

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_BILLING=true
ENABLE_NOTIFICATIONS=true
ENABLE_WEBSOCKETS=true
ENABLE_CACHING=true

# Performance Configuration
CACHE_TTL=3600
QUEUE_CONCURRENCY=5
MAX_CONCURRENT_EXECUTIONS=10

# Development Configuration
DEBUG=true
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

# Production Security (set these in production)
HELMET_ENABLED=true
TRUST_PROXY=false
SECURE_COOKIES=false

# Vector Database (for knowledge base)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=tempo-knowledge-base

# Analytics and Tracking
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=your-s3-backup-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1