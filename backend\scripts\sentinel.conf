# Redis Sentinel configuration for SynapseAI
# High availability setup

port 26379

# Sentinel announce settings
sentinel announce-ip 127.0.0.1
sentinel announce-port 26379

# Monitor the master Redis instance
sentinel monitor mymaster redis 6379 1
sentinel auth-pass mymaster redis123

# Failover settings
sentinel down-after-milliseconds mymaster 5000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 10000

# Notification scripts (optional)
# sentinel notification-script mymaster /var/redis/notify.sh
# sentinel client-reconfig-script mymaster /var/redis/reconfig.sh

# Logging
logfile /var/log/sentinel.log
loglevel notice

# Security
# requirepass sentinel123
# sentinel auth-user mymaster default

# Deny dangerous commands
sentinel deny-scripts-reconfig yes
